## FEATURE:

[描述要开发的Java功能 - 具体说明功能需求、业务场景和技术要求]
1、支持主产品传入【电商退货】ed-m-0076，主产品数据同步：外单wbs31位新增枚举；waybillsign 31=【N】；

2、修改：
- 识别收货信息中的一、二、三级地址编码&名称，即省、市、县编码&名称修改项，即省、市、县编码&名称修改项对比需要参考代码中修改项对比的方法。
- 当用户业务身份为快递C2C时，主产品=【电商退货】时，揽收前，不支持修改收货信息中的一、二、三级地址编码&名称，即省、市、县编码&名称；揽收后允许修改；

3、不支持E卡支付：【电商退货】主产品写pos台账的WaybillSign为0；


4、接单消息：订单接单成功结果消息通知，增加attachmentInfos

实现需要参考下面的功能提示词
## EXAMPLES:

[说明`core/examples/`文件夹中的相关示例，解释如何参考这些代码模式和最佳实践]

## DOCUMENTATION:

[列出开发过程中需要参考的文档资源：
- API文档链接
- 第三方库文档
- 企业内部文档
- 相关技术规范]

### 功能提示词

#### 1. 新主产品支持与数据同步

##### 功能描述
为OMS系统添加新的主产品类型支持，包括产品枚举定义、外单标识映射和数据同步机制。

##### 实现要点
- **产品枚举扩展**: 在`ProductEnum`中添加新产品类型
- **外单映射配置**: 在`WaybillInfoMappingUtil`中添加产品标识映射逻辑
- **数据同步机制**: 确保新产品在各个数据流转环节的正确识别

##### 关键代码模式
```java
// 1. 产品枚举定义
NEW_PRODUCT("ed-m-xxxx", "产品名称", 标识位置, "标识值"),

// 2. 外单映射逻辑
if (ProductEnum.NEW_PRODUCT.getSign().equals(String.valueOf(markUtil.charAt(31)))) {
    mainProduct.setProductName(ProductEnum.NEW_PRODUCT.getDesc());
    mainProduct.setProductNo(ProductEnum.NEW_PRODUCT.getCode());
}

// 3. 静态注册表更新（自动处理）
private static final Map<String, ProductEnum> registry = new HashMap();
static {
    Iterator iterator = EnumSet.allOf(ProductEnum.class).iterator();
    while (iterator.hasNext()) {
        ProductEnum typeEnum = (ProductEnum) iterator.next();
        registry.put(typeEnum.getCode(), typeEnum);
    }
}
```

##### 涉及文件
- `jdl-oms-express-domain-spec/src/main/java/cn/jdl/oms/express/domain/spec/dict/ProductEnum.java`
- `jdl-oms-express-domain-infrastructure/src/main/java/cn/jdl/oms/express/domain/infrs/ohs/locals/message/pl/waybill/WaybillInfoMappingUtil.java`

#### 2. 修改项识别方法

##### 功能描述
实现订单修改项的精确识别。

##### 实现要点
- **变更检测方法**: 在`ChangedPropertyDelegate`中实现各种变更检测方法
- **字段级变更**: 提供细粒度的字段变更检测能力

##### 关键代码模式
```java

// 通用变更检测模式
public boolean specificFieldHaveChange() {
    if (changedPropertyMap == null) {
        return false;
    }
    return changedPropertyMap.get(ModifyItemConfigEnum.SPECIFIC_FIELD.getCode()) != null;
}
```

##### 涉及文件
- `jdl-oms-express-domain-model/src/main/java/cn/jdl/oms/express/domain/vo/modify/ChangedPropertyDelegate.java`

#### 3. 修改规则守则

##### 功能描述
基于产品类型和订单状态实现修改权限控制，确保业务规则的正确执行。

##### 实现要点
- **状态检查**: 基于订单状态（如揽收前后）进行修改权限判断
- **产品类型限制**: 针对特定产品类型实施修改限制
- **异常处理**: 提供清晰的业务异常信息和错误提示

##### 关键代码模式
```java

// 通用业务规则检查模式
private void validateBusinessRule(ExpressOrderModel orderModel, ChangedPropertyDelegate changedPropertyDelegate) {
    // 1. 状态检查
    if (!isValidStatus(orderModel.getOrderSnapshot().getOrderStatus())) {
        throw new BusinessDomainException(ERROR_CODE).withCustom("当前状态不允许修改");
    }
    
    // 2. 产品类型检查
    if (isRestrictedProduct(orderModel.getProductDelegate().getMajorProductNo())) {
        throw new BusinessDomainException(ERROR_CODE).withCustom("当前产品类型不允许修改");
    }
    
    // 3. 字段变更检查
    if (hasRestrictedFieldChange(changedPropertyDelegate)) {
        throw new BusinessDomainException(ERROR_CODE).withCustom("不允许修改限制字段");
    }
}
```

##### 涉及文件
- `jdl-oms-express-horz-extension/src/main/java/cn/jdl/oms/express/horz/ext/white/ModifyWhiteExtension.java`
- `jdl-oms-express-b2c-extension/src/main/java/cn/jdl/oms/express/b2c/extension/white/B2CModifyWhiteExtension.java`

#### 4. 支付方式限制

##### 功能描述
基于产品类型实现支付方式的限制，如禁用E卡支付等。

##### 实现要点
- **支付限制枚举**: 在`ECardDisableReasonEnum`中定义支付限制原因
- **支付校验逻辑**: 在询价和台账写入pos台账过程中对waybillsign的第一位赋值为0不支持e卡
- **错误信息提示**: 提供明确的支付限制原因说明

##### 关键代码模式
```java
// 1. 支付限制枚举定义
PRODUCT_PAYMENT_DISABLE("编码", "特定产品不支持特定支付方式"),


// 2. 不允许e卡支付参考实现代码
 if (valOrderModel.getPromotion() != null && !CollectionUtils.isEmpty(valOrderModel.getPromotion().getActivities())) {
            for (Activity activity : valOrderModel.getPromotion().getActivities()) {
                if ("GRADUATION_SEND".equals(activity.getActivityNo())) {
                    LOGGER.info("waybillSignZeroBit,不支持E卡，活动 ");
                    return ECardDisableReasonEnum.ACTIVITY_GRADUATION_SEND;
                }
            }
        }
```

##### 涉及文件
- `jdl-oms-express-shared-common/src/main/java/cn/jdl/oms/express/shared/common/dict/ECardDisableReasonEnum.java`
- 各种OrderBankFacadeTranslator类

#### 5. 接单消息字段扩展

##### 功能描述
在接单成功消息通知中增加新的字段信息，支持附件信息等扩展数据的传递。

##### 实现要点
- **消息结构扩展**: 在接单消息DTO中添加新字段
- **数据转换逻辑**: 在消息转换器中处理新字段的映射
- **向下兼容**: 确保新字段的添加不影响现有消息处理逻辑

##### 关键代码模式
```java
// 1. 消息字段扩展
private List<AttachmentInfo> attachmentInfos;

// 2. 消息转换逻辑
Optional.ofNullable(source.getAttachmentInfos()).ifPresent(attachmentInfos -> {
    List<AttachmentInfoDto> attachmentInfoDtos = attachmentInfos.stream()
        .map(this::convertAttachmentInfo)
        .collect(Collectors.toList());
    target.setAttachmentInfos(attachmentInfoDtos);
});

// 3. 通用字段转换模式
```

##### 涉及文件
- `jdl-oms-express-client-model/src/main/java/cn/jdl/oms/express/model/CreateExpressOrderRequest.java`
- 各种Translator类


#####3. 数据转换模式
```java
// 使用Optional处理空值
Optional.ofNullable(source.getField()).ifPresent(field -> {
    target.setField(convertField(field));
});

// 集合转换
List<TargetDto> targetList = sourceList.stream()
    .map(this::convertItem)
    .collect(Collectors.toList());
```

##### 4. 业务校验模式
```java
// 统一异常处理
if (!isValid(condition)) {
    throw new BusinessDomainException(UnifiedErrorSpec.Category.ERROR_TYPE)
        .withCustom("具体错误信息");
}

// 日志记录
LOGGER.error("业务校验失败, orderNo: {}, reason: {}", orderNo, reason);

## OTHER CONSIDERATIONS:

[其他考虑因素和特殊要求：
- 现有系统集成约束
- 性能要求
- 安全要求
- 企业中间件使用偏好
- AI开发时容易遗漏的要点]

## MIDDLEWARE HINTS:

[如果功能涉及特定中间件，可以提供关键词提示：
- 需要远程调用服务 → 会匹配JSF
- 需要消息队列处理 → 会匹配JMQ
- 需要缓存功能 → 会匹配JIMDB
- 需要配置管理 → 会匹配DUCC
- 需要日志记录 → 会匹配DongLog]


# 一、背景说明

在实际使用AI编程过程中，仅依靠通用项目规则往往无法满足具体业务需求，生成的代码准确性有限。通过为AI提供详细的代码模板，并明确标注参考代码片段及对应位置，可显著提升代码生成质量。AI能够基于已有结构和规范，生成更符合实际业务场景的代码，提高准确性和可维护性。

然而，创建通用功能模板具有较高难度。从零设计完善一个功能模板的工作量几乎等同于开发完整业务功能，需要深入理解业务逻辑并抽象共性，确保模板的通用性和扩展性。

借助历史数据资源（如PRD文档、代码提交记录）和AI工具辅助，我们可以从历史需求和代码中自动提取通用模板。通过分析归纳历史项目共性，AI能够快速构建高质量模板，降低人工成本，提升开发效率。

下面以一个新增电商退货主产品的需求进行主产品通用模版的实践。

# 二、主产品通用模版实践

## 1. prd内容

接单：支持主产品传入【电商退货】ed-m-0076；
修改：快递C2C，主产品=【电商退货】时，揽收前，不支持修改收货信息中的一、二、三级地址编码&名称，即省、市、县编码&名称；揽收后允许修改；
询价写台账：【电商退货】主产品不支持E卡支付；
数据同步：外单wbs31位新增枚举；waybillsign 31=【N】
接单消息：快递C2C接单消息中，九、订单接单成功结果消息通知，增加attachmentInfos


## 2. 新增主产品需求模板生成
### 2.1 提示词

基于电商退货需求文档（路径：`jdl-oms-express-web/电商退货`）及相关代码提交记录（`4ac5d479808a83ba06461861xxxx` 和 `c8dd5ebb6e8b27d1c82c29b84f305c2eecxxx`），整理生成通用代码功能点提示词文档。
功能要求如下：
- 支持新增主产品（涉及数据同步）。
- 修改项识别方法
- 修改规则守则
- 不支持 e 卡支付方式。
- 接单消息需新增字段。
  
请注意，功能点描述需保持通用性，不与当前特定需求强绑定。

### 2.2 生成的文档

### 1. 新主产品支持与数据同步

#### 功能描述
为系统添加新的主产品类型支持，包括产品枚举定义、外部订单标识映射和数据同步机制。

#### 实现要点
- **产品枚举扩展**：在`ProductEnum`中添加新产品类型
- **外部订单映射配置**：在`WaybillInfoMappingUtil`中添加产品标识映射逻辑
- **数据同步机制**：确保新产品在各个数据流转环节的正确识别
##### 关键代码模式
```java
// 1. 产品枚举定义
NEW_PRODUCT("ed-m-xxxx", "产品名称", 标识位置, "标识值"),

// 2. 外单映射逻辑
if (ProductEnum.NEW_PRODUCT.getSign().equals(String.valueOf(markUtil.charAt(31)))) {
    mainProduct.setProductName(ProductEnum.NEW_PRODUCT.getDesc());
    mainProduct.setProductNo(ProductEnum.NEW_PRODUCT.getCode());
}

// 3. 静态注册表更新（自动处理）
private static final Map<String, ProductEnum> registry = new HashMap();
static {
    Iterator iterator = EnumSet.allOf(ProductEnum.class).iterator();
    while (iterator.hasNext()) {
        ProductEnum typeEnum = (ProductEnum) iterator.next();
        registry.put(typeEnum.getCode(), typeEnum);
    }
}
```

##### 涉及文件
- `jdl-oms-express-domain-spec/src/main/java/cn/jdl/oms/express/domain/spec/dict/ProductEnum.java`
- `jdl-oms-express-domain-infrastructure/src/main/java/cn/jdl/oms/express/domain/infrs/ohs/locals/message/pl/waybill/WaybillInfoMappingUtil.java`

#### 2. 修改项识别方法

##### 功能描述
实现订单修改项的精确识别。

##### 实现要点
- **变更检测方法**: 在`ChangedPropertyDelegate`中实现各种变更检测方法
- **字段级变更**: 提供细粒度的字段变更检测能力

##### 关键代码模式
```java

// 通用变更检测模式
public boolean specificFieldHaveChange() {
    if (changedPropertyMap == null) {
        return false;
    }
    return changedPropertyMap.get(ModifyItemConfigEnum.SPECIFIC_FIELD.getCode()) != null;
}
```

##### 涉及文件
- `jdl-oms-express-domain-model/src/main/java/cn/jdl/oms/express/domain/vo/modify/ChangedPropertyDelegate.java`

#### 3. 修改规则守则

##### 功能描述
基于产品类型和订单状态实现修改权限控制，确保业务规则的正确执行。

##### 实现要点
- **状态检查**: 基于订单状态（如揽收前后）进行修改权限判断
- **产品类型限制**: 针对特定产品类型实施修改限制
- **异常处理**: 提供清晰的业务异常信息和错误提示

##### 关键代码模式
```java

// 通用业务规则检查模式
private void validateBusinessRule(ExpressOrderModel orderModel, ChangedPropertyDelegate changedPropertyDelegate) {
    // 1. 状态检查
    if (!isValidStatus(orderModel.getOrderSnapshot().getOrderStatus())) {
        throw new BusinessDomainException(ERROR_CODE).withCustom("当前状态不允许修改");
    }
    
    // 2. 产品类型检查
    if (isRestrictedProduct(orderModel.getProductDelegate().getMajorProductNo())) {
        throw new BusinessDomainException(ERROR_CODE).withCustom("当前产品类型不允许修改");
    }
    
    // 3. 字段变更检查
    if (hasRestrictedFieldChange(changedPropertyDelegate)) {
        throw new BusinessDomainException(ERROR_CODE).withCustom("不允许修改限制字段");
    }
}
```

##### 涉及文件
- `jdl-oms-express-horz-extension/src/main/java/cn/jdl/oms/express/horz/ext/white/ModifyWhiteExtension.java`
- `jdl-oms-express-b2c-extension/src/main/java/cn/jdl/oms/express/b2c/extension/white/B2CModifyWhiteExtension.java`

#### 4. 支付方式限制

##### 功能描述
基于产品类型实现支付方式的限制，如禁用E卡支付等。

##### 实现要点
- **支付限制枚举**: 在`ECardDisableReasonEnum`中定义支付限制原因
- **支付校验逻辑**: 在询价和台账写入pos台账过程中对waybillsign的第一位赋值为0不支持e卡
- **错误信息提示**: 提供明确的支付限制原因说明

##### 关键代码模式
```java
// 1. 支付限制枚举定义
PRODUCT_PAYMENT_DISABLE("编码", "特定产品不支持特定支付方式"),


// 2. 不允许e卡支付参考实现代码
 if (valOrderModel.getPromotion() != null && !CollectionUtils.isEmpty(valOrderModel.getPromotion().getActivities())) {
            for (Activity activity : valOrderModel.getPromotion().getActivities()) {
                if ("GRADUATION_SEND".equals(activity.getActivityNo())) {
                    LOGGER.info("waybillSignZeroBit,不支持E卡，活动 ");
                    return ECardDisableReasonEnum.ACTIVITY_GRADUATION_SEND;
                }
            }
        }
```

##### 涉及文件
- `jdl-oms-express-shared-common/src/main/java/cn/jdl/oms/express/shared/common/dict/ECardDisableReasonEnum.java`
- 各种OrderBankFacadeTranslator类

#### 5. 接单消息字段扩展

##### 功能描述
在接单成功消息通知中增加新的字段信息，支持附件信息等扩展数据的传递。

##### 实现要点
- **消息结构扩展**: 在接单消息DTO中添加新字段
- **数据转换逻辑**: 在消息转换器中处理新字段的映射
- **向下兼容**: 确保新字段的添加不影响现有消息处理逻辑

##### 关键代码模式
```java
// 1. 消息字段扩展
private List<AttachmentInfo> attachmentInfos;

// 2. 消息转换逻辑
Optional.ofNullable(source.getAttachmentInfos()).ifPresent(attachmentInfos -> {
    List<AttachmentInfoDto> attachmentInfoDtos = attachmentInfos.stream()
        .map(this::convertAttachmentInfo)
        .collect(Collectors.toList());
    target.setAttachmentInfos(attachmentInfoDtos);
});

// 3. 通用字段转换模式
```

##### 涉及文件
- `jdl-oms-express-client-model/src/main/java/cn/jdl/oms/express/model/CreateExpressOrderRequest.java`
- 各种Translator类

#### 6. 异常校验模式
```java
// 统一异常处理
if (!isValid(condition)) {
    throw new BusinessDomainException(UnifiedErrorSpec.Category.ERROR_TYPE)
        .withCustom("具体错误信息");
}

// 日志记录
LOGGER.error("业务校验失败, orderNo: {}, reason: {}", orderNo, reason);
```
## 3. 生成项目projectInfo

